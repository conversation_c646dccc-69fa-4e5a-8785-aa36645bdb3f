/** @type {import('tailwindcss').Config} */
export default {
	content: ['./src/**/*.{html,js,svelte,ts}'],
	theme: {
		extend: {
			colors: {
				// Exact color specifications from requirements
				background: '#F8FAFC', // slate-50
				text: '#1E293B', // slate-800
				heading: '#0F172A', // slate-900
				primary: '#7C3AED', // violet-600
				'primary-hover': '#6D28D9', // violet-700
			},
			fontFamily: {
				// Font specifications from requirements
				sans: ['Inter', 'sans-serif'],
				display: ['Monument Extended', 'sans-serif'],
			},
			letterSpacing: {
				tighter: '-0.05em',
			},
		},
	},
	plugins: [],
}
