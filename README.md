# Hitez - Born Digital. Built Creative.

A high-performance, minimalist, single-page website for the creative technology agency "Hitez". Built with SvelteKit 5, GSAP animations, and Tailwind CSS following exact design specifications.

## 🚀 Features

- **Modern Tech Stack**: SvelteKit 5 with TypeScript, Tailwind CSS 4, and GSAP 3
- **Sophisticated Animations**: GSAP-powered animations with ScrollTrigger for immersive user experience
- **Professional Design**: Clean, light-mode design with strategic use of whitespace
- **Performance Optimized**: Lightweight and fast-loading with optimized animations
- **Responsive Layout**: Fully responsive across all devices

## 🎨 Design System

**Colors:**
- Background: `slate-50` (#F8FAFC)
- Text: `slate-800` (#1E293B)
- Headings: `slate-900` (#0F172A)
- Primary: `violet-600` (#7C3AED)
- Primary Hover: `violet-700` (#6D28D9)

**Typography:**
- Font Sans: 'Inter', sans-serif
- Font Display: 'Monument Extended', sans-serif (for large headlines)

## 📱 Four Key Sections

1. **Hero**: Full-viewport section with animated Z logo, staggered headline reveals, and smooth CTA
2. **Philosophy**: Two-column layout explaining "We Don't Follow The Rules. We Redefine The Game."
3. **Services**: Three service cards showcasing Interactive Installations, AR/VR Experiences, and Creative Tech Consulting
4. **Call to Action**: Clean, centered section with "Have an ambitious idea?" messaging

## 🛠 Development

### Prerequisites
- Node.js 18+
- npm, pnpm, or yarn

### Getting Started

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Open in browser
npm run dev -- --open
```

### Build for Production

```bash
# Create production build
npm run build

# Preview production build
npm run preview
```

## 🎯 Technical Highlights

- **GSAP Integration**: Animations initialized in `onMount` lifecycle with proper cleanup
- **ScrollTrigger**: Advanced scroll-based animations with mobile optimizations
- **Accessibility**: Screen reader support, keyboard navigation, and motion preferences
- **TypeScript**: Full type safety throughout the application
- **Tailwind CSS**: Utility-first styling with custom configuration

## 📦 Dependencies

- SvelteKit ^2.22.2 (Svelte 5 ^5.35.2)
- Tailwind CSS ^4.1.11
- GSAP ^3.13.0
- TypeScript ^5.8.3
- Vite ^6.3.5

## 🎨 Customization

The design system is configured in `tailwind.config.js` with custom colors, fonts, and spacing. GSAP animations can be customized in the main page component.

## 📄 License

Private project for Hitez creative agency.
