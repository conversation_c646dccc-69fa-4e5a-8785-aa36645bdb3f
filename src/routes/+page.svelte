<script lang="ts">
	import { onMount } from 'svelte';
	import { gsap } from 'gsap';
	import { ScrollTrigger } from 'gsap/ScrollTrigger';
	import Logo from '$lib/Logo.svelte';

	// Register GSAP plugins
	gsap.registerPlugin(ScrollTrigger);

	onMount(() => {
		// Hero section animations - On Load Timeline
		const heroTl = gsap.timeline();

		// 1. SVG logo draws/fades/scales in
		heroTl.from('.logo-group', {
			scale: 0,
			opacity: 0,
			duration: 1,
			ease: 'back.out(1.7)'
		})
		// 2. Stagger-reveal the words in the H1 headline
		.from('.hero-word', {
			y: 100,
			opacity: 0,
			duration: 0.8,
			stagger: 0.2,
			ease: 'power3.out'
		}, '-=0.5')
		// 3. Fade-in and slide-up the sub-headline paragraph
		.from('.hero-subheadline', {
			y: 50,
			opacity: 0,
			duration: 0.6,
			ease: 'power2.out'
		}, '-=0.3')
		// 4. Fade-in and scale-in the CTA button
		.from('.hero-cta', {
			scale: 0.8,
			opacity: 0,
			duration: 0.5,
			ease: 'back.out(1.7)'
		}, '-=0.2');

		// Philosophy section - ScrollTrigger animation
		ScrollTrigger.create({
			trigger: '.philosophy-section',
			start: 'top 80%',
			animation: gsap.from('.philosophy-left > *', {
				y: 60,
				opacity: 0,
				duration: 0.8,
				stagger: 0.2,
				ease: 'power2.out'
			})
		});

		// Services section - ScrollTrigger animation
		ScrollTrigger.create({
			trigger: '.services-section',
			start: 'top 80%',
			animation: gsap.from('.service-card', {
				y: 80,
				opacity: 0,
				scale: 0.9,
				duration: 0.8,
				stagger: 0.15,
				ease: 'power2.out'
			})
		});

		// CTA section - ScrollTrigger animation
		ScrollTrigger.create({
			trigger: '.cta-section',
			start: 'top 80%',
			animation: gsap.from('.cta-content > *', {
				y: 50,
				opacity: 0,
				duration: 0.6,
				stagger: 0.1,
				ease: 'power2.out'
			})
		});

		// Button hover animations
		const ctaButtons = document.querySelectorAll('.cta-button');
		ctaButtons.forEach(button => {
			button.addEventListener('mouseenter', () => {
				gsap.to(button, { scale: 1.05, duration: 0.3, ease: 'power2.out' });
			});
			button.addEventListener('mouseleave', () => {
				gsap.to(button, { scale: 1, duration: 0.3, ease: 'power2.out' });
			});
		});

		// Cleanup function
		return () => {
			ScrollTrigger.getAll().forEach(trigger => trigger.kill());
		};
	});

	// Smooth scroll function for CTA button
	function scrollToContact() {
		document.querySelector('.cta-section')?.scrollIntoView({
			behavior: 'smooth'
		});
	}
</script>

<!-- SECTION 1: HERO -->
<section class="min-h-screen flex flex-col items-center justify-center text-center p-8">
	<div class="max-w-4xl">
		<!-- Logo -->
		<Logo class="text-primary mb-8" />

		<!-- Headline (H1) -->
		<h1 class="text-5xl lg:text-7xl font-display font-bold text-heading tracking-tighter">
			<span class="hero-word inline-block">Born</span>
			<span class="hero-word inline-block"> Digital.</span>
			<br>
			<span class="hero-word inline-block">Built</span>
			<span class="hero-word inline-block"> Creative.</span>
		</h1>

		<!-- Sub-headline (P) -->
		<p class="hero-subheadline mt-6 text-lg lg:text-xl text-text max-w-2xl mx-auto">
			We are a new generation of creators, blending disruptive technology and bold artistry to build unforgettable experiences.
		</p>

		<!-- Call to Action (A) -->
		<a
			href="#contact"
			class="hero-cta cta-button mt-10 inline-block bg-primary hover:bg-primary-hover text-white font-bold py-4 px-10 rounded-lg transition-colors"
			on:click|preventDefault={scrollToContact}
		>
			Start a Project
		</a>
	</div>
</section>

<!-- SECTION 2: PHILOSOPHY (The "Why") -->
<section class="philosophy-section py-24 lg:py-32">
	<div class="container mx-auto px-8 grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
		<!-- Left Column (Text) -->
		<div class="philosophy-left">
			<!-- Headline (H2) -->
			<h2 class="text-4xl lg:text-5xl font-display font-bold text-heading tracking-tighter">
				We Don't Follow The Rules. We Redefine The Game.
			</h2>

			<!-- Paragraph 1 (P) -->
			<p class="mt-6 text-lg text-text">
				Traditional events often feel passive and predictable. They talk at audiences, not with them. We saw a gap—a need for experiences that are not just seen, but felt, shared, and remembered.
			</p>

			<!-- Paragraph 2 (P) -->
			<p class="mt-4 text-lg text-text">
				At Hitez, we use technology as our paintbrush and strategy as our canvas. Our approach is rooted in human psychology and powered by code, creating genuine interactions that drive real-world results.
			</p>
		</div>

		<!-- Right Column (Visual) -->
		<div class="philosophy-visual">
			<!-- Abstract, looping animation placeholder -->
			<div class="w-full h-96 bg-gradient-to-br from-primary/10 to-primary/30 rounded-2xl flex items-center justify-center">
				<div class="w-32 h-32 border-4 border-primary rounded-full animate-spin opacity-60"></div>
			</div>
		</div>
	</div>
</section>

<!-- SECTION 3: SERVICES (The "What") -->
<section class="services-section bg-slate-100 py-24 lg:py-32">
	<!-- Centered header area -->
	<div class="text-center max-w-3xl mx-auto px-8">
		<!-- Headline (H2) -->
		<h2 class="text-4xl font-display font-bold text-heading tracking-tight">What We Build</h2>

		<!-- Paragraph (P) -->
		<p class="mt-4 text-lg text-text">
			We transform ambitious ideas into tangible, high-impact digital and physical experiences.
		</p>
	</div>

	<!-- Responsive 3-column grid for service cards -->
	<div class="mt-16 container mx-auto px-8 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
		<!-- Service Card 1 -->
		<div class="service-card bg-white p-8 rounded-lg shadow-sm">
			<!-- Icon -->
			<svg class="h-12 w-12 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
			</svg>

			<!-- H3 -->
			<h3 class="mt-4 text-xl font-bold text-heading">Interactive Installations</h3>

			<!-- P -->
			<p class="mt-2 text-text">
				Unforgettable physical touchpoints that connect your brand to the real world in a digitally-native way.
			</p>
		</div>

		<!-- Service Card 2 -->
		<div class="service-card bg-white p-8 rounded-lg shadow-sm">
			<!-- Icon -->
			<svg class="h-12 w-12 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
			</svg>

			<!-- H3 -->
			<h3 class="mt-4 text-xl font-bold text-heading">AR/VR Experiences</h3>

			<!-- P -->
			<p class="mt-2 text-text">
				Immersive worlds and augmented realities that erase the line between a screen and a universe.
			</p>
		</div>

		<!-- Service Card 3 -->
		<div class="service-card bg-white p-8 rounded-lg shadow-sm">
			<!-- Icon -->
			<svg class="h-12 w-12 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
			</svg>

			<!-- H3 -->
			<h3 class="mt-4 text-xl font-bold text-heading">Creative Tech Consulting</h3>

			<!-- P -->
			<p class="mt-2 text-text">
				Your strategic partner in turning wild ideas into executable, technically-sound project blueprints.
			</p>
		</div>
	</div>
</section>

<!-- SECTION 4: CALL TO ACTION (The Final Action) -->
<section id="contact" class="cta-section py-24 lg:py-32">
	<!-- Centered container -->
	<div class="container mx-auto px-8 text-center max-w-3xl">
		<div class="cta-content">
			<!-- Headline (H2) -->
			<h2 class="text-4xl lg:text-5xl font-display font-bold text-heading tracking-tighter">
				Have an ambitious idea?
			</h2>

			<!-- Paragraph (P) -->
			<p class="mt-4 text-lg text-text">
				We live for those. Let's build something unforgettable together.
			</p>

			<!-- Contact Button (A) -->
			<a
				href="mailto:<EMAIL>"
				class="cta-button mt-10 inline-block bg-primary hover:bg-primary-hover text-white font-bold py-4 px-10 rounded-lg transition-colors"
			>
				Get In Touch
			</a>

			<!-- Social Links -->
			<div class="mt-8 flex justify-center space-x-6 text-slate-400">
				<a href="https://linkedin.com/company/hitez" class="hover:text-primary transition-colors">
					LinkedIn
				</a>
				<a href="https://instagram.com/hitez" class="hover:text-primary transition-colors">
					Instagram
				</a>
				<a href="https://twitter.com/hitez" class="hover:text-primary transition-colors">
					Twitter
				</a>
			</div>
		</div>
	</div>
</section>
